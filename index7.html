<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>自定义页面</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>

    <div class="control-panel">
        <!-- 主要功能区 -->
        <div class="control-section normal-view-section">
            <button id="viewNormalBtn" class="view-mode-btn primary-btn">View Normal Albums</button>
            <div class="controls-row">
                <div class="control-group">
                    <label for="mainFolderSelectorEl">Select Month:</label>
                    <select id="mainFolderSelectorEl"></select>
                </div>
                <div class="control-group">
                    <label for="subFolderSelectorEl">Select Sub-folder:</label>
                    <select id="subFolderSelectorEl"></select>
                </div>
                <div class="control-group sort-selector-container">
                    <label for="sortSelectorEl">Sort by:</label>
                    <select id="sortSelectorEl">
                        <option value="date">Date</option>
                        <option value="name">Name</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- 收藏功能区 -->
        <div class="control-section favorites-section">
            <button id="viewFavoritesBtn" class="view-mode-btn">View All Favorites</button>
            <div class="controls-row">
                <div class="control-group">
                    <label for="favoritesSelectorEl">Favorites:</label>
                    <select id="favoritesSelectorEl">
                        <option value="">Select from favorites</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- 标签功能区 -->
        <div class="control-section tags-section">
            <button id="viewTagsBtn" class="view-mode-btn">View Tags</button>
            <div class="controls-row">
                <div class="control-group">
                    <label for="tagsSelectorEl">Tags:</label>
                    <select id="tagsSelectorEl">
                        <option value="">Select a tag</option>
                    </select>
                </div>
                <div class="control-group">
                    <select id="taggedFoldersSelectorEl">
                        <option value="all">Select all tagged folders</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <div class="column-selector">
        <button data-columns="1">1列</button>
        <button data-columns="2">2列</button>
        <button data-columns="3" class="active">3列</button>
        <button data-columns="4">4列</button>
    </div>


    <!-- 添加一个锚点用于滚动到图片区域顶部 -->
    <div id="gallery-top-anchor"></div>

    <div class="gallery" id="imageGallery">
        <div id="imageRow" class="row"></div>
    </div>

    <div class="load-more-container">
        <button class="load-more-btn" id="loadMoreBtn" style="display: block; margin: 20px auto;">Load More</button>
    </div>

    <!-- 右下角按钮组 -->
    <div class="floating-buttons">
        <button id="backToTop" class="tooltip" data-tooltip="返回顶部" title="返回顶部"></button>
        <button id="favoriteBtn" class="tooltip" data-tooltip="收藏当前文件夹" title="收藏当前文件夹"></button>
        <button id="nextFolderBtn" class="tooltip" data-tooltip="下一个文件夹" title="下一个文件夹"></button>
    </div>

    <!-- 收藏夹弹窗 -->
    <div class="modal-backdrop"></div>
    <div class="favorites-modal">
        <h3>收藏夹</h3>
        <div class="favorites-list"></div>
    </div>

    <script src="fancybox.umd.js"></script>
    <script src="imagesloaded.pkgd.min.js"></script>
    <script src="masonry.pkgd.min.js"></script>
    <link rel="stylesheet" href="fancybox.css"/>
    <link rel="stylesheet" href="bootstrap.min.css">
    <script src="script.js"></script>
</body>
</html>