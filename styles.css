.image-wrapper img {
    max-width: 100%;
    height: auto;
}
.custom-column {
    margin-bottom: 20px;
}
.column-selector, .main-folder-selector, .sub-folder-selector, .favorites-selector {
    margin: 15px 0;
    text-align: center;
    padding: 0 15px;
}
.column-selector button {
    padding: 10px 20px;
    margin: 5px;
    border: 1px solid #ddd;
    background: #fff;
    cursor: pointer;
    border-radius: 4px;
    font-size: 16px;
    min-width: 60px;
}
.column-selector button.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
}
.main-folder-selector label, .sub-folder-selector label, .favorites-selector label {
    margin-right: 10px;
}
/* 返回顶部按钮样式 */
#backToTop {
    bottom: 30px;
    right: 30px;
    display: none;
    opacity: 0;
    visibility: hidden;
    background-color: rgba(0, 123, 255, 0.9);
}
#backToTop.show {
    display: block;
    opacity: 1;
    visibility: visible;
}
#backToTop:hover {
    background-color: #0056b3;
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(0,0,0,0.2);
}
#backToTop::before {
    content: "↑";
    font-weight: bold;
}

/* 收藏按钮样式 */
#favoriteBtn {
    bottom: 30px;
    right: 90px;
    background-color: rgba(255, 193, 7, 0.9);
}
#favoriteBtn:hover {
    background-color: #ffc107;
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(0,0,0,0.2);
}
#favoriteBtn::before {
    content: "☆";
    font-weight: bold;
}
#favoriteBtn.active {
    background-color: #ffc107;
}
#favoriteBtn.active::before {
    content: "★";
}

/* 下一个文件夹按钮样式 */
#nextFolderBtn {
    bottom: 30px;
    right: 150px;
    background-color: rgba(40, 167, 69, 0.9);
}
#nextFolderBtn:hover {
    background-color: #28a745;
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(0,0,0,0.2);
}
#nextFolderBtn::before {
    content: "→";
    font-weight: bold;
}

/* 工具提示 */
.tooltip {
    position: relative;
}
.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    padding: 5px 10px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    border-radius: 4px;
    font-size: 14px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}
.tooltip:hover::after {
    opacity: 1;
    visibility: visible;
    bottom: calc(100% + 10px);
}

/* 收藏夹弹窗样式 */
.favorites-modal {
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 1000;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

/* 标签选择器样式 */
.tags-selector {
    margin: 20px 0;
    text-align: center;
}
.tags-selector label {
    margin-right: 10px;
}
.tags-selector select {
    margin-right: 20px;
}

/* 移动端大尺寸优化 - 针对所有移动设备 */
@media screen and (max-width: 600px) {
    .tags-selector {
        padding: 0 40px !important;
        margin: 50px 0 !important;
    }
    
    .tags-selector label {
        font-size: 80px !important;
        margin: 0 0 30px 0 !important;
        font-weight: bold !important;
    }
    
    /* 针对特定label的移动端强制样式 */
    label[for="tagsSelectorEl"],
    label[for="taggedFoldersSelectorEl"] {
        font-size: 80px !important;
        font-weight: bold !important;
        margin-bottom: 30px !important;
        display: block !important;
        color: #000 !important;
        line-height: 1.2 !important;
        background-color: yellow !important; /* 调试用黄色背景 */
    }
    
    #viewTagsBtn {
        padding: 40px 60px !important;
        font-size: 48px !important;
        min-height: 120px !important;
        border-radius: 20px !important;
        border-width: 3px !important;
        width: 100% !important;
        max-width: none !important;
    }
}

.favorites-modal h3 {
    margin-top: 0;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}
.favorites-list {
    list-style: none;
    padding: 0;
    margin: 0;
}
.favorites-list li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
}
.favorites-list li:hover {
    background-color: #f8f9fa;
}
.favorites-list li button {
    background: none;
    border: none;
    color: #dc3545;
    cursor: pointer;
    padding: 5px;
}
.modal-backdrop {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 999;
}
/* 收藏选择器样式 */
.favorites-selector {
    margin: 20px 0;
    text-align: center;
}
.favorites-selector label {
    margin-right: 10px;
}
.favorites-selector select {
    margin-right: 20px;
}
.view-mode-btn {
    padding: 8px 16px;
    border: 1px solid #ddd;
    background: #fff;
    cursor: pointer;
    transition: all 0.3s ease;
}
.view-mode-btn.active {
    background: #ffc107;
    color: white;
    border-color: #ffc107;
}

/* 下拉框样式优化 */
select {
    width: 100%;
    max-width: 300px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: white;
    font-size: 16px; /* 防止iOS自动放大 */
    margin: 5px 0;
}

/* 移动端大尺寸优化 - 针对所有移动设备 */
@media screen and (max-width: 600px) {
    select {
        padding: 30px 25px !important;
        font-size: 64px !important;
        border-radius: 15px !important;
        border-width: 4px !important;
        margin: 20px 0 !important;
        min-height: 120px !important;
    }
    
    .column-selector button {
        padding: 40px 60px !important;
        font-size: 48px !important;
        min-height: 120px !important;
        min-width: 240px !important;
        max-width: 360px !important;
        border-radius: 16px !important;
        border-width: 4px !important;
    }
    
    .main-folder-selector, .sub-folder-selector, .favorites-selector {
        padding: 0 40px !important;
        margin: 50px 0 !important;
    }
    
    .main-folder-selector label, 
    .sub-folder-selector label,
    .favorites-selector label {
        font-size: 80px !important;
        margin: 0 0 30px 0 !important;
        font-weight: bold !important;
    }
    
    .sort-selector-container label {
        font-size: 80px !important;
        margin: 0 0 30px 0 !important;
        font-weight: bold !important;
    }
    
    /* 针对特定label的移动端强制样式 */
    label[for="mainFolderSelectorEl"],
    label[for="subFolderSelectorEl"], 
    label[for="favoritesSelectorEl"],
    label[for="sortSelectorEl"] {
        font-size: 80px !important;
        font-weight: bold !important;
        margin-bottom: 30px !important;
        display: block !important;
        color: #000 !important;
        line-height: 1.2 !important;
        background-color: yellow !important; /* 调试用黄色背景 */
    }
    
    .favorites-modal h3 {
        font-size: 56px !important;
        margin: 0 0 40px 0 !important;
        padding-bottom: 30px !important;
    }
    
    .favorites-list li {
        padding: 50px !important;
        font-size: 44px !important;
        min-height: 140px !important;
    }
    
    .favorites-list li button {
        padding: 40px !important;
        font-size: 56px !important;
        min-width: 120px !important;
        min-height: 120px !important;
        border-radius: 16px !important;
    }
    
    .view-mode-btn {
        padding: 40px 60px !important;
        font-size: 48px !important;
        min-height: 120px !important;
        border-radius: 20px !important;
        border-width: 3px !important;
    }
    
    /* 专门针对收藏夹按钮 */
    #viewFavoritesBtn {
        padding: 40px 60px !important;
        font-size: 48px !important;
        min-height: 120px !important;
        border-radius: 20px !important;
        border-width: 3px !important;
        width: 100% !important;
        max-width: none !important;
    }
}

/* 响应式按钮样式 */
#backToTop, #favoriteBtn, #nextFolderBtn {
    position: fixed;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: none;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    transition: all 0.3s ease;
    font-size: 24px;
    line-height: 50px;
    color: white;
    z-index: 9999;
    display: block;
    visibility: visible;
    opacity: 1;
}

/* 移动端样式调整 */
@media (max-width: 768px) {
    .main-folder-selector, .sub-folder-selector, .favorites-selector {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 10px;
    }
    
    .main-folder-selector label, 
    .sub-folder-selector label,
    .favorites-selector label {
        margin: 0;
    }
    
    select {
        width: 100%;
        max-width: none;
        margin: 5px 0;
    }
    
    .column-selector {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 5px;
    }
    
    .column-selector button {
        flex: 1;
        min-width: 100px;
        max-width: 150px;
        padding: 15px 25px;
        font-size: 20px;
        min-height: 50px;
    }
    
    #backToTop, #favoriteBtn, #nextFolderBtn {
        width: 60px;
        height: 60px;
        line-height: 60px;
        font-size: 28px;
    }
    
    #backToTop {
        bottom: 20px;
        right: 20px;
    }
    
    #favoriteBtn {
        bottom: 90px;
        right: 20px;
    }
    
    #nextFolderBtn {
        bottom: 160px;
        right: 20px;
    }
}

/* 移动端超大按钮优化 - 竖排放置 */
@media screen and (max-width: 600px) {
    #backToTop, #favoriteBtn, #nextFolderBtn {
        width: 200px !important;
        height: 200px !important;
        line-height: 200px !important;
        font-size: 80px !important;
        box-shadow: 0 12px 40px rgba(0,0,0,0.3) !important;
    }
    
    #backToTop {
        bottom: 50px !important;
        right: 50px !important;
    }
    
    #favoriteBtn {
        bottom: 270px !important;
        right: 50px !important;
    }
    
    #nextFolderBtn {
        bottom: 490px !important;
        right: 50px !important;
    }
    
    /* 工具提示在移动端隐藏 */
    .tooltip::after {
        display: none !important;
    }
}

/* 备用移动端样式 - 强制应用 */
@media screen and (max-device-width: 600px) {
    select {
        padding: 30px 25px !important;
        font-size: 64px !important;
        border-radius: 15px !important;
        border-width: 4px !important;
        margin: 20px 0 !important;
        min-height: 120px !important;
        background-color: #f8f9fa !important; /* 调试用背景色 */
    }
    
    .column-selector button {
        padding: 40px 60px !important;
        font-size: 48px !important;
        min-height: 120px !important;
        min-width: 240px !important;
        background-color: #e9ecef !important; /* 调试用背景色 */
    }
    
    #backToTop, #favoriteBtn, #nextFolderBtn {
        width: 200px !important;
        height: 200px !important;
        line-height: 200px !important;
        font-size: 80px !important;
    }
    
    #backToTop {
        bottom: 50px !important;
        right: 50px !important;
    }
    
    #favoriteBtn {
        bottom: 270px !important;
        right: 50px !important;
    }
    
    #nextFolderBtn {
        bottom: 490px !important;
        right: 50px !important;
    }
    
    /* 备用label强制样式 */
    label[for="mainFolderSelectorEl"],
    label[for="subFolderSelectorEl"], 
    label[for="favoritesSelectorEl"],
    label[for="sortSelectorEl"] {
        font-size: 80px !important;
        font-weight: bold !important;
        margin-bottom: 30px !important;
        display: block !important;
        color: #000 !important;
        line-height: 1.2 !important;
        background-color: yellow !important; /* 调试用黄色背景 */
    }
    
    /* 备用收藏夹按钮样式 */
    .view-mode-btn,
    #viewFavoritesBtn {
        padding: 40px 60px !important;
        font-size: 48px !important;
        min-height: 120px !important;
        border-radius: 20px !important;
        border-width: 3px !important;
        width: 100% !important;
        max-width: none !important;
    }
}

/* 优化收藏夹弹窗在移动端的显示 */
.favorites-modal {
    width: 95%;
    max-width: 500px;
    max-height: 90vh;
    padding: 15px;
}

@media (max-width: 768px) {
    .favorites-modal {
        width: 100%;
        height: 100%;
        max-height: 100vh;
        margin: 0;
        border-radius: 0;
        top: 0;
        left: 0;
        transform: none;
        display: flex;
        flex-direction: column;
    }
    
    .favorites-list {
        flex: 1;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
    }
    
    .favorites-list li {
        padding: 20px;
        font-size: 20px;
    }
    
    .favorites-list li button {
        padding: 15px;
        font-size: 24px;
        min-width: 50px;
        min-height: 50px;
    }
}

/* 排序选择器样式 */
.sort-selector-container {
    margin-top: 10px;
}

/* 优化排序选择器在移动端的显示 */
@media (max-width: 768px) {
    .sort-selector-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-top: 10px;
    }
    
    .sort-selector-container label {
        margin: 0 0 5px 0;
    }
    
    #sortSelectorEl {
        width: 100%;
        max-width: 300px;
        margin-top: 5px;
    }
}

/* 控制面板样式 */
.control-panel {
    padding: 15px;
    margin-bottom: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.control-section {
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.control-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.controls-row {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 10px;
}

.control-group {
    flex: 1;
    min-width: 200px;
    display: flex;
    flex-direction: column;
}

.control-group label {
    margin-bottom: 5px;
    font-weight: 500;
}

.control-group select {
    width: 100%;
}

.primary-btn {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
    font-weight: bold;
    padding: 10px 20px;
}

.view-mode-btn {
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    border: 1px solid #ddd;
    background: #fff;
}

.view-mode-btn.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

/* 锚点样式 */
#gallery-top-anchor {
    position: relative;
    top: -20px;
    visibility: hidden;
}

/* 移动端样式调整 */
@media (max-width: 768px) {
    .controls-row {
        flex-direction: column;
        gap: 10px;
    }
    
    .control-group {
        width: 100%;
    }
    
    .view-mode-btn {
        width: 100%;
        padding: 15px;
        font-size: 18px;
    }
}

/* 视频缩略图样式 */
.video-wrapper {
    position: relative;
}

.video-thumbnail {
    position: relative;
    width: 100%;
    height: 100%;
    background-color: #000;
    overflow: hidden;
}

.video-thumbnail img,
.video-thumbnail video {
    width: 100%;
    height: auto;
    object-fit: cover;
    display: block;
}

.play-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2;
}

.play-icon:after {
    content: '';
    display: block;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 10px 0 10px 20px;
    border-color: transparent transparent transparent white;
    margin-left: 5px;
}

/* 隐藏视频预览元素 */
.video-preview {
    opacity: 0;
    position: absolute;
    width: 1px;
    height: 1px;
}

/* 确保视频在Fancybox中响应式显示并居中 */
.fancybox__content video {
    max-width: 100%;
    max-height: 80vh;
    height: auto;
    margin: 0 auto;
    display: block;
}

/* 确保Fancybox内容垂直居中 */
.fancybox__container .fancybox__content {
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 图片和视频占位符样式 */
.image-placeholder {
    width: 100%;
    height: 0;
    padding-bottom: 75%; /* 4:3 比例 */
    background-color: #f0f0f0;
    position: relative;
}

.image-placeholder::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 30px;
    height: 30px;
    border: 3px solid #ccc;
    border-radius: 50%;
    border-top-color: #999;
    animation: spin 1s linear infinite;
}

.video-placeholder {
    width: 100%;
    height: 0;
    padding-bottom: 56.25%; /* 16:9 比例 */
    background-color: #f0f0f0;
    position: relative;
}

.video-placeholder::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 30px;
    height: 30px;
    border: 3px solid #ccc;
    border-radius: 50%;
    border-top-color: #999;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: translate(-50%, -50%) rotate(360deg); }
}

/* 懒加载观察点样式 */
#lazy-load-observer {
    height: 10px;
    width: 100%;
    visibility: hidden;
}

/* Fancybox移动端按钮优化 */
@media (max-width: 768px) {
    /* 关闭按钮优化 - 匹配多种可能的选择器 */
    .fancybox__container .f-button.is-close-btn,
    .fancybox__container .f-button[data-fancybox-close],
    .fancybox__container button[data-fancybox-close] {
        width: 60px !important;
        height: 60px !important;
        --f-button-width: 60px !important;
        --f-button-height: 60px !important;
        --f-button-svg-width: 30px !important;
        --f-button-svg-height: 30px !important;
        --f-button-svg-stroke-width: 3 !important;
        top: 10px !important;
        right: 10px !important;
        background-color: rgba(0, 0, 0, 0.8) !important;
        border-radius: 50% !important;
        z-index: 9999 !important;
    }

    /* 关闭按钮内的SVG图标 */
    .fancybox__container .f-button[data-fancybox-close] svg,
    .fancybox__container button[data-fancybox-close] svg {
        width: 30px !important;
        height: 30px !important;
        stroke-width: 3 !important;
    }

    /* 导航按钮优化 */
    .fancybox__nav .f-button {
        --f-button-width: 70px !important;
        --f-button-height: 70px !important;
        --f-button-svg-width: 35px !important;
        --f-button-svg-height: 35px !important;
        --f-button-svg-stroke-width: 3 !important;
        --f-button-next-pos: 15px !important;
        --f-button-prev-pos: 15px !important;
        background-color: rgba(0, 0, 0, 0.6) !important;
    }

    /* 工具栏按钮优化 */
    .fancybox__toolbar .f-button {
        width: 210px !important;
        height: 210px !important;
        --f-button-width: 210px !important;
        --f-button-height: 210px !important;
        --f-button-svg-width: 105px !important;
        --f-button-svg-height: 105px !important;
        --f-button-svg-stroke-width: 9 !important;
        margin: 15px !important;
    }

    /* 工具栏按钮内的SVG图标 */
    .fancybox__toolbar .f-button svg {
        width: 105px !important;
        height: 105px !important;
        stroke-width: 9 !important;
    }

    /* 页码信息栏优化 - 变得非常大 */
    .fancybox__infobar {
        font-size: 48px !important;
        line-height: 60px !important;
        padding: 15px 20px !important;
        background-color: rgba(0, 0, 0, 0.8) !important;
        border-radius: 10px !important;
        color: white !important;
        font-weight: bold !important;
        margin: 10px !important;
    }

    /* 缩略图按钮优化 */
    .fancybox__thumbs .f-thumbs__slide__button {
        min-height: 60px !important;
        min-width: 80px !important;
    }
}

/* 超小屏幕设备进一步优化 */
@media (max-width: 480px) {
    /* 关闭按钮进一步放大 */
    .fancybox__container .f-button.is-close-btn,
    .fancybox__container .f-button[data-fancybox-close],
    .fancybox__container button[data-fancybox-close] {
        width: 80px !important;
        height: 80px !important;
        --f-button-width: 80px !important;
        --f-button-height: 80px !important;
        --f-button-svg-width: 40px !important;
        --f-button-svg-height: 40px !important;
        --f-button-svg-stroke-width: 4 !important;
        top: 15px !important;
        right: 15px !important;
    }

    /* 关闭按钮内的SVG图标 */
    .fancybox__container .f-button[data-fancybox-close] svg,
    .fancybox__container button[data-fancybox-close] svg {
        width: 40px !important;
        height: 40px !important;
        stroke-width: 4 !important;
    }

    /* 导航按钮进一步放大 */
    .fancybox__nav .f-button {
        --f-button-width: 90px !important;
        --f-button-height: 90px !important;
        --f-button-svg-width: 45px !important;
        --f-button-svg-height: 45px !important;
        --f-button-svg-stroke-width: 4 !important;
        --f-button-next-pos: 20px !important;
        --f-button-prev-pos: 20px !important;
    }

    /* 工具栏按钮进一步放大 */
    .fancybox__toolbar .f-button {
        width: 270px !important;
        height: 270px !important;
        --f-button-width: 270px !important;
        --f-button-height: 270px !important;
        --f-button-svg-width: 135px !important;
        --f-button-svg-height: 135px !important;
        --f-button-svg-stroke-width: 12 !important;
        margin: 20px !important;
    }

    /* 工具栏按钮内的SVG图标 */
    .fancybox__toolbar .f-button svg {
        width: 135px !important;
        height: 135px !important;
        stroke-width: 12 !important;
    }

    /* 页码信息栏进一步放大 */
    .fancybox__infobar {
        font-size: 64px !important;
        line-height: 80px !important;
        padding: 20px 30px !important;
        background-color: rgba(0, 0, 0, 0.9) !important;
        border-radius: 15px !important;
        margin: 15px !important;
        font-weight: bold !important;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8) !important;
    }

    /* 增加按钮触摸区域 */
    .fancybox__container .f-button::before {
        content: '';
        position: absolute;
        top: -15px;
        left: -15px;
        right: -15px;
        bottom: -15px;
        z-index: -1;
    }
}

/* 为触摸设备优化按钮间距和大小 */
@media (pointer: coarse) {
    .fancybox__container .f-button.is-close-btn,
    .fancybox__container .f-button[data-fancybox-close],
    .fancybox__container button[data-fancybox-close] {
        width: 70px !important;
        height: 70px !important;
        --f-button-width: 70px !important;
        --f-button-height: 70px !important;
        --f-button-svg-width: 35px !important;
        --f-button-svg-height: 35px !important;
    }

    /* 关闭按钮内的SVG图标 */
    .fancybox__container .f-button[data-fancybox-close] svg,
    .fancybox__container button[data-fancybox-close] svg {
        width: 35px !important;
        height: 35px !important;
        stroke-width: 3 !important;
    }

    .fancybox__nav .f-button {
        --f-button-width: 80px !important;
        --f-button-height: 80px !important;
        --f-button-svg-width: 40px !important;
        --f-button-svg-height: 40px !important;
    }

    /* 触摸设备工具栏按钮优化 */
    .fancybox__toolbar .f-button {
        width: 80px !important;
        height: 80px !important;
        --f-button-width: 80px !important;
        --f-button-height: 80px !important;
        --f-button-svg-width: 40px !important;
        --f-button-svg-height: 40px !important;
        --f-button-svg-stroke-width: 3 !important;
        margin: 6px !important;
    }

    /* 触摸设备工具栏按钮内的SVG图标 */
    .fancybox__toolbar .f-button svg {
        width: 40px !important;
        height: 40px !important;
        stroke-width: 3 !important;
    }

    /* 触摸设备页码信息栏优化 */
    .fancybox__infobar {
        font-size: 56px !important;
        line-height: 70px !important;
        padding: 18px 25px !important;
        background-color: rgba(0, 0, 0, 0.85) !important;
        border-radius: 12px !important;
        margin: 12px !important;
        font-weight: bold !important;
    }
}
